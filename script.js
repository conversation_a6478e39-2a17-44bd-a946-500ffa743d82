const boardElement = document.getElementById('board');
const resetButton = document.getElementById('reset-button');
const playerTimerElement = document.getElementById('player-timer');
const aiTimerElement = document.getElementById('ai-timer');
const aiThinkingElement = document.getElementById('ai-thinking');
const boardSize = 15;
let board = [];
let currentPlayer = 'black';
let gameOver = false;
let playerTime = 0;
let aiTime = 0;
let playerTimerInterval;
let aiTimerInterval;

// 初始化棋盘
function initBoard() {
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    boardElement.innerHTML = '';
    
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            const cell = document.createElement('div');
            cell.classList.add('cell');
            cell.dataset.row = row;
            cell.dataset.col = col;
            boardElement.appendChild(cell);
        }
    }

    currentPlayer = 'black';
    gameOver = false;
    resetTimers();
    startPlayerTimer();
}

// 处理单元格点击
function handleCellClick(event) {
    if (gameOver || currentPlayer !== 'black') return;
    
    const cell = event.target.closest('.cell');
    if (!cell) return;
    
    const row = parseInt(cell.dataset.row);
    const col = parseInt(cell.dataset.col);
    if (board[row][col]) return;

    stopPlayerTimer();
    board[row][col] = 'black';
    cell.classList.add('black');

    if (checkWin(row, col, 'black')) {
        gameOver = true;
        setTimeout(() => alert('恭喜你，你赢了!'), 100);
        return;
    }

    currentPlayer = 'white';
    aiMove();
}

// AI移动
async function aiMove() {
    if (gameOver) return;
    aiThinkingElement.style.display = 'block';
    startAiTimer();

    const bestMove = await findBestMoveAsync();
    stopAiTimer();
    aiThinkingElement.style.display = 'none';

    if (bestMove) {
        const { row, col } = bestMove;
        board[row][col] = 'white';
        const cell = document.querySelector(`[data-row='${row}'][data-col='${col}']`);
        cell.classList.add('white');

        if (checkWin(row, col, 'white')) {
            gameOver = true;
            setTimeout(() => alert('AI 赢了!'), 100);
            return;
        }
        
        currentPlayer = 'black';
        startPlayerTimer();
    } else if (board.flat().every(cell => cell !== null)) {
        gameOver = true;
        setTimeout(() => alert('平局!'), 100);
    } else {
        currentPlayer = 'black';
        startPlayerTimer();
    }
}

// 极简AI算法 - 只保留核心逻辑
async function findBestMoveAsync() {
    // 1. 检查AI是否能获胜
    const winMove = findWinMove('white');
    if (winMove) return winMove;

    // 2. 检查是否需要阻挡玩家获胜
    const blockMove = findWinMove('black');
    if (blockMove) return blockMove;

    // 3. 寻找最佳位置
    return findBestPosition();
}

// 寻找获胜位置
function findWinMove(player) {
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (!board[r][c]) {
                board[r][c] = player;
                if (checkWin(r, c, player)) {
                    board[r][c] = null;
                    return { row: r, col: c };
                }
                board[r][c] = null;
            }
        }
    }
    return null;
}

// 寻找最佳位置
function findBestPosition() {
    const center = Math.floor(boardSize / 2);

    // 如果中心空着，优先下中心
    if (!board[center][center]) {
        return { row: center, col: center };
    }

    // 寻找已有棋子周围的位置
    const candidates = [];
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c]) {
                // 检查周围8个位置
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        if (dr === 0 && dc === 0) continue;
                        const nr = r + dr;
                        const nc = c + dc;
                        if (isValidPosition(nr, nc) && !board[nr][nc]) {
                            candidates.push({ row: nr, col: nc });
                        }
                    }
                }
            }
        }
    }

    // 如果有候选位置，选择第一个
    if (candidates.length > 0) {
        return candidates[0];
    }

    // 最后选择第一个空位
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (!board[r][c]) {
                return { row: r, col: c };
            }
        }
    }

    return null;
}

// 计时器功能
function resetTimers() {
    clearInterval(playerTimerInterval);
    clearInterval(aiTimerInterval);
    playerTime = 0;
    aiTime = 0;
    playerTimerElement.textContent = '玩家用时: 0s';
    aiTimerElement.textContent = 'AI用时: 0s';
}

function startPlayerTimer() {
    playerTimerInterval = setInterval(() => {
        playerTime++;
        playerTimerElement.textContent = `玩家用时: ${playerTime}s`;
    }, 1000);
}

function stopPlayerTimer() {
    clearInterval(playerTimerInterval);
}

function startAiTimer() {
    aiTimerInterval = setInterval(() => {
        aiTime++;
        aiTimerElement.textContent = `AI用时: ${aiTime}s`;
    }, 1000);
}

function stopAiTimer() {
    clearInterval(aiTimerInterval);
}

// 胜负检查
function checkWin(row, col, player) {
    const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
    ];

    for (const [dx, dy] of directions) {
        let count = 1;
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
                count++;
            } else break;
        }
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
                count++;
            } else break;
        }
        if (count >= 5) return true;
    }
    return false;
}

// 辅助函数
function isValidPosition(row, col) {
    return row >= 0 && row < boardSize && col >= 0 && col < boardSize;
}

// 事件绑定
boardElement.addEventListener('click', handleCellClick);
resetButton.addEventListener('click', initBoard);

// 初始化游戏
initBoard();

// 计算连续棋子数量
function countConsecutive(row, col, dx, dy, player) {
    let count = 1;

    // 向前计算
    for (let i = 1; i < 5; i++) {
        const newRow = row + i * dx;
        const newCol = col + i * dy;
        if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
            count++;
        } else break;
    }

    // 向后计算
    for (let i = 1; i < 5; i++) {
        const newRow = row - i * dx;
        const newCol = col - i * dy;
        if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
            count++;
        } else break;
    }

    return count;
}

// 事件绑定
boardElement.addEventListener('click', handleCellClick);
resetButton.addEventListener('click', initBoard);

// 初始化游戏
initBoard();













// 辅助函数：检查位置是否有效
function isValidPosition(row, col) {
    return row >= 0 && row < boardSize && col >= 0 && col < boardSize;
}

// 计算连续棋子数量
function countConsecutive(row, col, dx, dy, player) {
    let count = 1;

    // 向前计算
    for (let i = 1; i < 5; i++) {
        const newRow = row + i * dx;
        const newCol = col + i * dy;
        if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
            count++;
        } else {
            break;
        }
    }

    // 向后计算
    for (let i = 1; i < 5; i++) {
        const newRow = row - i * dx;
        const newCol = col - i * dy;
        if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
            count++;
        } else {
            break;
        }
    }

    return count;
}



// 事件绑定
boardElement.addEventListener('click', handleCellClick);
resetButton.addEventListener('click', initBoard);

// 初始化游戏
initBoard();

function checkWin(row, col, player) {
    const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
    ];

    for (const [dx, dy] of directions) {
        let count = 1;
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && board[newRow][newCol] === player) {
                count++;
            } else {
                break;
            }
        }
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && board[newRow][newCol] === player) {
                count++;
            } else {
                break;
            }
        }
        if (count >= 5) {
            return true;
        }
    }
    return false;
}


function getThreatDescription(level) {
    if (level >= 10000) return '立即获胜';
    if (level >= 9000) return '活四';
    if (level >= 5000) return '冲四';
    if (level >= 3000) return '活三';
    if (level >= 2000) return '跳活三';
    if (level >= 1000) return '眠三';
    if (level >= 300) return '活二';
    if (level >= 200) return '跳活二';
    if (level >= 100) return '眠二';
    return '轻微威胁';
}

// 检查活四
function checkActiveFour(row, col, player) {
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        let count = 1; // 包含当前位置
        let frontBlocked = false;
        let backBlocked = false;

        // 向前计数
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;

            if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== player) {
                if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== null) {
                    frontBlocked = true;
                }
                break;
            }
            count++;
        }

        // 向后计数
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;

            if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== player) {
                if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== null) {
                    backBlocked = true;
                }
                break;
            }
            count++;
        }

        // 活四：4个连子且至少一端不被阻挡
        if (count === 4 && (!frontBlocked || !backBlocked)) {
            return true;
        }
    }

    return false;
}

// 检查活三
function checkActiveThree(row, col, player) {
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        let count = 1; // 包含当前位置

        let frontEmpty = 0;
        let backEmpty = 0;

        // 向前检查
        for (let i = 1; i <= 4; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;

            if (!isValidPosition(newRow, newCol)) {
                break;
            }

            if (board[newRow][newCol] === player) {
                count++;
            } else if (board[newRow][newCol] === null) {
                frontEmpty++;
                break;
            } else {
                break;
            }
        }

        // 向后检查
        for (let i = 1; i <= 4; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;

            if (!isValidPosition(newRow, newCol)) {
                break;
            }

            if (board[newRow][newCol] === player) {
                count++;
            } else if (board[newRow][newCol] === null) {
                backEmpty++;
                break;
            } else {
                break;
            }
        }

        // 活三：3个连子且两端都有空位可以延伸
        if (count === 3 && frontEmpty > 0 && backEmpty > 0) {
            return true;
        }
    }

    return false;
}

// 测试威胁检测功能
function testThreatDetection() {
    console.log('=== 威胁检测测试 ===');

    // 清空棋盘
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));

    // 测试场景1：活三
    const center = Math.floor(boardSize / 2);
    board[center][center] = 'black';
    board[center][center + 1] = 'black';
    board[center][center + 2] = 'black';

    console.log('测试场景1：黑棋在中心形成活三');
    console.log('黑棋位置：', `(${center},${center})`, `(${center},${center + 1})`, `(${center},${center + 2})`);

    let criticalDefense = findCriticalDefense();
    console.log('关键防守位置：', criticalDefense);

    // 清空棋盘，测试场景2：跳活三
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    board[center][center] = 'black';
    board[center][center + 2] = 'black';
    board[center][center + 3] = 'black';

    console.log('\n测试场景2：黑棋形成跳活三 X_XX');
    criticalDefense = findCriticalDefense();
    console.log('关键防守位置：', criticalDefense);

    // 清空棋盘，测试场景3：活四
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    board[center][center] = 'black';
    board[center][center + 1] = 'black';
    board[center][center + 2] = 'black';
    board[center][center + 3] = 'black';

    console.log('\n测试场景3：黑棋形成活四');
    criticalDefense = findCriticalDefense();
    console.log('关键防守位置：', criticalDefense);

    // 恢复棋盘
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    console.log('\n=== 测试完成 ===');
}

// 增强的威胁检测 - 添加更多威胁模式检测
function findAdvancedThreats() {
    console.log('=== 高级威胁检测 ===');

    const allThreats = [];

    // 检查现有棋盘上的威胁模式
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === 'black') {
                // 从每个黑棋位置检查威胁
                const threats = checkThreatsFromPosition(row, col, 'black');
                allThreats.push(...threats);
            }
        }
    }

    // 去重并排序
    const uniqueThreats = removeDuplicateThreats(allThreats);
    uniqueThreats.sort((a, b) => b.level - a.level);

    if (uniqueThreats.length > 0) {
        console.log('发现的威胁:', uniqueThreats);
        return uniqueThreats[0];
    }

    return null;
}

// 从特定位置检查威胁
function checkThreatsFromPosition(row, col, player) {
    const threats = [];
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        // 检查这个方向上需要防守的位置
        const defensePositions = findDefensePositionsInDirection(row, col, dx, dy, player);
        threats.push(...defensePositions);
    }

    return threats;
}

// 在特定方向寻找防守位置
function findDefensePositionsInDirection(row, col, dx, dy, player) {
    const threats = [];

    // 检查从当前位置延伸的各种威胁模式
    for (let range = 1; range <= 5; range++) {
        for (let offset = -range; offset <= range; offset++) {
            const checkRow = row + offset * dx;
            const checkCol = col + offset * dy;

            if (isValidPosition(checkRow, checkCol) && board[checkRow][checkCol] === null) {
                // 模拟在此位置放置棋子，检查威胁等级
                board[checkRow][checkCol] = player;
                const threatLevel = evaluateThreatLevel(checkRow, checkCol, player);
                board[checkRow][checkCol] = null;

                if (threatLevel >= 1000) { // 只关注重要威胁
                    threats.push({
                        row: checkRow,
                        col: checkCol,
                        level: threatLevel,
                        description: getThreatDescription(threatLevel)
                    });
                }
            }
        }
    }

    return threats;
}

// 去除重复威胁
function removeDuplicateThreats(threats) {
    const seen = new Set();
    const unique = [];

    for (const threat of threats) {
        const key = `${threat.row},${threat.col}`;
        if (!seen.has(key)) {
            seen.add(key);
            unique.push(threat);
        }
    }

    return unique;
}

// 在控制台中可以调用 testThreatDetection() 来测试
window.testThreatDetection = testThreatDetection;

resetButton.addEventListener('click', initBoard);

initBoard();
