const boardElement = document.getElementById('board');
const resetButton = document.getElementById('reset-button');
const playerTimerElement = document.getElementById('player-timer');
const aiTimerElement = document.getElementById('ai-timer');
const aiThinkingElement = document.getElementById('ai-thinking');
const boardSize = 15;
let board = [];
let currentPlayer = 'black';
let gameOver = false;
let playerTime = 0;
let aiTime = 0;
let playerTimerInterval;
let aiTimerInterval;

// 初始化棋盘
function initBoard() {
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    boardElement.innerHTML = '';
    
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            const cell = document.createElement('div');
            cell.classList.add('cell');
            cell.dataset.row = row;
            cell.dataset.col = col;
            boardElement.appendChild(cell);
        }
    }

    currentPlayer = 'black';
    gameOver = false;
    resetTimers();
    startPlayerTimer();
}

// 处理单元格点击
function handleCellClick(event) {
    if (gameOver || currentPlayer !== 'black') return;
    
    const cell = event.target.closest('.cell');
    if (!cell) return;
    
    const row = parseInt(cell.dataset.row);
    const col = parseInt(cell.dataset.col);
    if (board[row][col]) return;

    stopPlayerTimer();
    board[row][col] = 'black';
    cell.classList.add('black');

    if (checkWin(row, col, 'black')) {
        gameOver = true;
        setTimeout(() => alert('恭喜你，你赢了!'), 100);
        return;
    }

    currentPlayer = 'white';
    aiMove();
}

// AI移动
async function aiMove() {
    if (gameOver) return;
    aiThinkingElement.style.display = 'block';
    startAiTimer();

    const bestMove = await findBestMoveAsync();
    stopAiTimer();
    aiThinkingElement.style.display = 'none';

    if (bestMove) {
        const { row, col } = bestMove;
        board[row][col] = 'white';
        const cell = document.querySelector(`[data-row='${row}'][data-col='${col}']`);
        cell.classList.add('white');

        if (checkWin(row, col, 'white')) {
            gameOver = true;
            setTimeout(() => alert('AI 赢了!'), 100);
            return;
        }
        
        currentPlayer = 'black';
        startPlayerTimer();
    } else if (board.flat().every(cell => cell !== null)) {
        gameOver = true;
        setTimeout(() => alert('平局!'), 100);
    } else {
        currentPlayer = 'black';
        startPlayerTimer();
    }
}

// 增强AI算法 - 提升棋力
async function findBestMoveAsync() {
    // 1. 检查AI是否能获胜
    const winMove = findWinMove('white');
    if (winMove) return winMove;

    // 2. 检查是否需要阻挡玩家获胜
    const blockMove = findWinMove('black');
    if (blockMove) return blockMove;

    // 3. 检查AI的活四威胁
    const aiFourMove = findFourMove('white');
    if (aiFourMove) return aiFourMove;

    // 4. 检查并阻挡玩家的活四威胁
    const blockFourMove = findFourMove('black');
    if (blockFourMove) return blockFourMove;

    // 5. 检查AI的活三威胁
    const aiThreeMove = findThreeMove('white');
    if (aiThreeMove) return aiThreeMove;

    // 6. 检查并阻挡玩家的活三威胁
    const blockThreeMove = findThreeMove('black');
    if (blockThreeMove) return blockThreeMove;

    // 7. 寻找最佳位置
    return findBestPosition();
}

// 寻找获胜位置
function findWinMove(player) {
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (!board[r][c]) {
                board[r][c] = player;
                if (checkWin(r, c, player)) {
                    board[r][c] = null;
                    return { row: r, col: c };
                }
                board[r][c] = null;
            }
        }
    }
    return null;
}

// 寻找活四位置
function findFourMove(player) {
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (!board[r][c]) {
                board[r][c] = player;
                if (checkFour(r, c, player)) {
                    board[r][c] = null;
                    return { row: r, col: c };
                }
                board[r][c] = null;
            }
        }
    }
    return null;
}

// 寻找活三位置
function findThreeMove(player) {
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (!board[r][c]) {
                board[r][c] = player;
                if (checkThree(r, c, player)) {
                    board[r][c] = null;
                    return { row: r, col: c };
                }
                board[r][c] = null;
            }
        }
    }
    return null;
}

// 寻找最佳位置
function findBestPosition() {
    const center = Math.floor(boardSize / 2);

    // 如果中心空着，优先下中心
    if (!board[center][center]) {
        return { row: center, col: center };
    }

    // 收集候选位置并评分
    const candidates = [];
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (!board[r][c]) {
                // 检查是否在已有棋子周围2格范围内
                if (isNearExistingPieces(r, c, 2)) {
                    const score = evaluatePosition(r, c);
                    candidates.push({ row: r, col: c, score });
                }
            }
        }
    }

    // 如果没有候选位置，扩大搜索范围
    if (candidates.length === 0) {
        for (let r = 0; r < boardSize; r++) {
            for (let c = 0; c < boardSize; c++) {
                if (!board[r][c]) {
                    const score = evaluatePosition(r, c);
                    candidates.push({ row: r, col: c, score });
                }
            }
        }
    }

    // 按分数排序，选择最佳位置
    if (candidates.length > 0) {
        candidates.sort((a, b) => b.score - a.score);
        return { row: candidates[0].row, col: candidates[0].col };
    }

    return null;
}

// 计时器功能
function resetTimers() {
    clearInterval(playerTimerInterval);
    clearInterval(aiTimerInterval);
    playerTime = 0;
    aiTime = 0;
    playerTimerElement.textContent = '玩家用时: 0s';
    aiTimerElement.textContent = 'AI用时: 0s';
}

function startPlayerTimer() {
    playerTimerInterval = setInterval(() => {
        playerTime++;
        playerTimerElement.textContent = `玩家用时: ${playerTime}s`;
    }, 1000);
}

function stopPlayerTimer() {
    clearInterval(playerTimerInterval);
}

function startAiTimer() {
    aiTimerInterval = setInterval(() => {
        aiTime++;
        aiTimerElement.textContent = `AI用时: ${aiTime}s`;
    }, 1000);
}

function stopAiTimer() {
    clearInterval(aiTimerInterval);
}

// 胜负检查
function checkWin(row, col, player) {
    const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
    ];

    for (const [dx, dy] of directions) {
        let count = 1;
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
                count++;
            } else break;
        }
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
                count++;
            } else break;
        }
        if (count >= 5) return true;
    }
    return false;
}

// 检查活四
function checkFour(row, col, player) {
    const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
    ];

    for (const [dx, dy] of directions) {
        let count = 1;
        let frontBlocked = false;
        let backBlocked = false;

        // 向前计数
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
                count++;
            } else {
                if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== null) {
                    frontBlocked = true;
                }
                break;
            }
        }

        // 向后计数
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
                count++;
            } else {
                if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== null) {
                    backBlocked = true;
                }
                break;
            }
        }

        // 活四：4个连子且至少一端不被阻挡
        if (count === 4 && (!frontBlocked || !backBlocked)) {
            return true;
        }
    }
    return false;
}

// 检查活三
function checkThree(row, col, player) {
    const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
    ];

    for (const [dx, dy] of directions) {
        let count = 1;
        let frontEmpty = 0;
        let backEmpty = 0;

        // 向前检查
        for (let i = 1; i <= 4; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (!isValidPosition(newRow, newCol)) break;

            if (board[newRow][newCol] === player) {
                count++;
            } else if (board[newRow][newCol] === null) {
                frontEmpty++;
                break;
            } else {
                break;
            }
        }

        // 向后检查
        for (let i = 1; i <= 4; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (!isValidPosition(newRow, newCol)) break;

            if (board[newRow][newCol] === player) {
                count++;
            } else if (board[newRow][newCol] === null) {
                backEmpty++;
                break;
            } else {
                break;
            }
        }

        // 活三：3个连子且两端都有空位可以延伸
        if (count === 3 && frontEmpty > 0 && backEmpty > 0) {
            return true;
        }
    }
    return false;
}

// 辅助函数
function isValidPosition(row, col) {
    return row >= 0 && row < boardSize && col >= 0 && col < boardSize;
}

// 检查位置是否在已有棋子附近
function isNearExistingPieces(row, col, range) {
    for (let r = Math.max(0, row - range); r <= Math.min(boardSize - 1, row + range); r++) {
        for (let c = Math.max(0, col - range); c <= Math.min(boardSize - 1, col + range); c++) {
            if (board[r][c]) {
                return true;
            }
        }
    }
    return false;
}

// 评估位置价值
function evaluatePosition(row, col) {
    let score = 0;
    const center = Math.floor(boardSize / 2);

    // 1. 中心位置加分
    const distanceFromCenter = Math.abs(row - center) + Math.abs(col - center);
    score += (boardSize - distanceFromCenter) * 10;

    // 2. 模拟放置AI棋子，评估攻击价值
    board[row][col] = 'white';
    score += evaluateLines(row, col, 'white') * 2;
    board[row][col] = null;

    // 3. 模拟放置对手棋子，评估防守价值
    board[row][col] = 'black';
    score += evaluateLines(row, col, 'black') * 1.5;
    board[row][col] = null;

    return score;
}

// 评估某个位置的连线价值
function evaluateLines(row, col, player) {
    let score = 0;
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        let count = 1;
        let openEnds = 0;

        // 向前计数
        let frontCount = 0;
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (isValidPosition(newRow, newCol)) {
                if (board[newRow][newCol] === player) {
                    frontCount++;
                } else if (board[newRow][newCol] === null) {
                    if (frontCount === 0) openEnds++;
                    break;
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        // 向后计数
        let backCount = 0;
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (isValidPosition(newRow, newCol)) {
                if (board[newRow][newCol] === player) {
                    backCount++;
                } else if (board[newRow][newCol] === null) {
                    if (backCount === 0) openEnds++;
                    break;
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        count += frontCount + backCount;

        // 根据连子数量和开放端数量评分
        if (count >= 4) {
            score += 1000;
        } else if (count === 3) {
            if (openEnds >= 2) score += 500; // 活三
            else if (openEnds === 1) score += 100; // 眠三
        } else if (count === 2) {
            if (openEnds >= 2) score += 50; // 活二
            else if (openEnds === 1) score += 10; // 眠二
        }
    }

    return score;
}

// 事件绑定
boardElement.addEventListener('click', handleCellClick);
resetButton.addEventListener('click', initBoard);

// 初始化游戏
initBoard();
